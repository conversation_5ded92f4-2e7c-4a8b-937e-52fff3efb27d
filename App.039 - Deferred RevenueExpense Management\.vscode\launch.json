{
    "version": "0.2.0",
    "configurations": [
        // {
        //     "name": "HC_050625",
        //     "type": "al",
        //     "request": "launch",
        //     "tenant": "ed380b86-ee80-43bd-9a7f-f04d3c53ab26",
        //     "environmentType": "Sandbox",
        //     "environmentName": "HC_050625",
        //     "startupObjectId": 71019082,
        //     "breakOnError": "All",
        //     "breakOnRecordWrite": "None",
        //     "launchBrowser": false,
        //     "enableSqlInformationDebugger": true,
        //     "enableLongRunningSqlStatements": true,
        //     "longRunningSqlStatementsThreshold": 500,
        // },
        {
            "name": "ERKSOITRON_DEV",
            "type": "al",
            "request": "launch",
            "tenant": "d16b496a-4648-40d9-bc65-1be4d931d0b1",
            "environmentType": "Sandbox",
            "environmentName": "HC_070725",
            "startupObjectId": 71019082,
            "breakOnError": "All",
            "breakOnRecordWrite": "None",
            "launchBrowser": false,
            "enableSqlInformationDebugger": true,
            "enableLongRunningSqlStatements": true,
            "longRunningSqlStatementsThreshold": 500,
            //"schemaUpdateMode": "ForceSync"
        },
        {
            "name": "SUMIKA_DEV4",
            "type": "al",
            "request": "launch",
            "tenant": "851f6719-46a2-43ac-8f03-4a84fc1fbffc",
            "environmentType": "Sandbox",
            "environmentName": "SUMIKA_DEV4",
            "startupObjectId": 71019082,
            "breakOnError": "All",
            "breakOnRecordWrite": "None",
            "launchBrowser": false,
            "enableSqlInformationDebugger": true,
            "enableLongRunningSqlStatements": true,
            "longRunningSqlStatementsThreshold": 500
        },
        // {
        //     "name": "DIRUI_DEV",
        //     "type": "al",
        //     "request": "launch",
        //     "tenant": "cc0e402e-bba8-4d63-9020-1ec47bd603a2",
        //     "environmentType": "Sandbox",
        //     "environmentName": "DIRUI_DEV",
        //     "startupObjectId": 71019082,
        //     "breakOnError": "All",
        //     "breakOnRecordWrite": "None",
        //     "launchBrowser": false,
        //     "enableSqlInformationDebugger": true,
        //     "enableLongRunningSqlStatements": true,
        //     "longRunningSqlStatementsThreshold": 500
        // },
        // {
        //     "name": "Erkport_Deniz",
        //     "type": "al",
        //     "request": "launch",
        //     "tenant": "ed380b86-ee80-43bd-9a7f-f04d3c53ab26",
        //     "environmentType": "Sandbox",
        //     "environmentName": "Deniz",
        //     "startupObjectId": 71019082,
        //     "breakOnError": "All",
        //     "breakOnRecordWrite": "None",
        //     "launchBrowser": false,
        //     "enableSqlInformationDebugger": true,
        //     "enableLongRunningSqlStatements": true,
        //     "longRunningSqlStatementsThreshold": 500,
        //     //"schemaUpdateMode": "ForceSync"
        // }
    ]
}