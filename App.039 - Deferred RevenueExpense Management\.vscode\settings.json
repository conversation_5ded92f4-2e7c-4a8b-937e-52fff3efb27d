{"alCodeActions.extractToLabelCreatesComment": true, "alCodeActions.publisherHasVarParametersOnly": true, "al.browser": "Chrome", "al.codeAnalyzers": ["${CodeCop}", "${UICop}", "${AppSourceCop}", "${analyzerFolder}BusinessCentral.LinterCop.dll"], "al.enableCodeActions": true, "al.enableCodeAnalysis": true, "ALTB.snippetTargetLanguage": "TRK", "alOutline.addDataItemToReportColumnName": true, "alOutline.autoGenerateFiles": true, "alOutline.defaultDataClassification": "Customer<PERSON><PERSON>nt", "alOutline.enableCodeCopFixes": true, "alOutline.fixCaseRemovesQuotesFromDataTypeIdentifiers": true, "alOutline.fixCodeCopMissingParenthesesOnSave": true, "alOutline.noEmptyLinesAtTheEndOfWizardGeneratedFiles": true, "CRS.DisableDefaultAlSnippets": true, "CRS.ObjectNameSuffix": " DRE INF", "alVarHelper.ignoreALSuffix": "DRE INF", "alNavigator.ignoreALSuffix": "DRE INF", "CRS.OnSaveAlFileAction": "Reorganize", "CRS.SkipWarningMessageOnRenameAll": true, "al.ruleSetPath": "infotek.ruleset.json"}