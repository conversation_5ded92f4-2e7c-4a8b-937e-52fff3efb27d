# İnfotek App Signing Guide

Complete guide for signing Business Central apps using Azure Key Vault for AppSource submission.

## 📋 Project Information

### App Details
- **App Name**: Deferred Revenue/Expense Management by İnfotek
- **Publisher**: İnfotek Yazılım ve Donanım A.Ş.
- **Latest Version**: ********
- **App File Location**: `.\Infotek Yazilim ve Donanim A.S._Deferred RevenueExpense Management by İnfotek_********.app`
- **Description**: Deferred Revenue/Expense Management App by İnfotek
- **Website**: https://www.infotek.com.tr

### Available App Versions
```
Infotek Yazilim ve Donanim A.S._Deferred RevenueExpense Management by İnfotek_********.app (Latest)
Infotek Yazilim ve Donanim A.S._Deferred RevenueExpense Management by İnfotek_25.0.0.4.app
Infotek Yazilim ve Donanim A.S._Deferred RevenueExpense Management by İnfotek_25.0.0.3.app
Infotek Yazilim ve Donanim A.S._Deferred RevenueExpense Management by İnfotek_25.0.0.2.app
Infotek Yazilim ve Donanim A.S._Deferred RevenueExpense Management by İnfotek_25.0.0.1.app
... (and 11 older versions)
```

## 🔐 Azure Key Vault Configuration

### Subscription & Tenant Details
- **Subscription**: MCPP Subscription
- **Subscription ID**: `a1373717-2e0b-40f1-a27d-537e4f1eaccc`
- **Tenant**: Infotek Yaz. ve Don. Hiz. A.S.
- **Tenant ID**: `d3dcd3d0-46e6-41be-8e63-c5fa8f564846`
- **Domain**: infotekas.com.tr

### Azure Resources
- **Resource Group**: `rg-infotek-appsigning`
- **Location**: East US
- **Key Vault Name**: `kv-infotek-signing-164`
- **Key Vault URL**: `https://kv-infotek-signing-164.vault.azure.net/`
- **SKU**: Premium (required for HSM certificates)

### Certificate Information
- **Certificate Name**: `infotek-codesigning-cert`
- **Source**: Sectigo Code Signing Certificate
- **File**: `SectigoCodeSigningCertificate.pfx`
- **Subject**: CN=Infotek Yazilim ve Donanim A.S., O=Infotek Yazilim ve Donanim A.S., S=İstanbul, C=TR
- **Expires**: 2026-04-30T23:59:59+00:00
- **Thumbprint**: 365D8E13901A2CEE12DFD19085E807BA824C4684

### Service Principal (Automation)
- **Name**: `sp-infotek-signing-5400`
- **Client ID**: `a4018b7f-fc13-4c32-90c4-c19fc3c83e1e`
- **Client Secret**: `****************************************` ⚠️ **KEEP SECURE**
- **Tenant ID**: `d3dcd3d0-46e6-41be-8e63-c5fa8f564846`

## 🛠️ Prerequisites & Tools

### Installed Tools
- ✅ **.NET SDK 8.0.413** - Installed
- ✅ **Azure CLI 2.76.0** - Installed  
- ✅ **Sign Tool v0.9.1-beta** - Installed globally

### Required Permissions
- **Key Vault Crypto Officer** - Assigned to user and service principal
- **Key Vault Certificates Officer** - Assigned to user and service principal

## 🚀 Quick Signing Commands

### Single App File Signing
```powershell
# Sign latest app file
sign code azure-key-vault `
  --azure-key-vault-url "https://kv-infotek-signing-164.vault.azure.net/" `
  --azure-key-vault-certificate "infotek-codesigning-cert" `
  --description "Deferred Revenue/Expense Management App by İnfotek" `
  --description-url "https://www.infotek.com.tr" `
  --verbosity Information `
  ".\Infotek Yazilim ve Donanim A.S._Deferred RevenueExpense Management by İnfotek_********.app"
```

### Batch Signing with Service Principal
```powershell
# Load configuration
. .\SigningConfig-Final.ps1

# Set environment variables for authentication
$env:AZURE_CLIENT_ID = $ClientId
$env:AZURE_CLIENT_SECRET = $ClientSecret  
$env:AZURE_TENANT_ID = $TenantId

# Sign with service principal
sign code azure-key-vault `
  --azure-key-vault-url $KeyVaultUrl `
  --azure-key-vault-certificate $CertificateName `
  --description $Description `
  --description-url $DescriptionUrl `
  --verbosity Information `
  "app-file.app"
```

### Batch Signing Script
```powershell
# Use the automated batch signing script
.\Sign-AppFiles.ps1 `
  -KeyVaultUrl "https://kv-infotek-signing-164.vault.azure.net/" `
  -CertificateName "infotek-codesigning-cert" `
  -ClientId "a4018b7f-fc13-4c32-90c4-c19fc3c83e1e" `
  -ClientSecret "****************************************" `
  -TenantId "d3dcd3d0-46e6-41be-8e63-c5fa8f564846" `
  -SignLatestOnly
```

## 📁 Project Files

### Configuration Files
- `SigningConfig-Final.ps1` - Complete Azure Key Vault configuration
- `SigningConfig.example.ps1` - Template configuration file
- `Sign-AppFiles.ps1` - Batch signing PowerShell script
- `Setup-Fresh.ps1` - Azure Key Vault setup script

### Certificate Files
- `Certificates/SectigoCodeSigningCertificate.pfx` - Main signing certificate
- `Certificates/ComodoCodeSigningCertificate.pfx` - Alternative certificate
- `Certificates/CertificateInfo.md` - Certificate information

### Documentation Files
- `README-Signing.md` - General signing setup guide
- `Azure-KeyVault-Setup.md` - Detailed Azure setup instructions
- `Infotek-App-Signing-Guide.md` - This comprehensive guide

## 🔒 Security Best Practices

### ⚠️ Critical Security Information
- **NEVER** commit the Client Secret to source control
- Store credentials in secure location (password manager, Azure Key Vault)
- Rotate Service Principal secrets regularly
- Use least privilege access principles
- Monitor Key Vault access logs

### Access Control
- Current user: `<EMAIL>`
- Service Principal: `sp-infotek-signing-5400`
- Roles: Key Vault Crypto Officer + Key Vault Certificates Officer

## 🔧 Troubleshooting

### Common Issues
1. **Certificate not found**: Verify certificate name and Key Vault URL
2. **Access denied**: Check RBAC roles and permissions
3. **Authentication failed**: Verify Client ID, Secret, and Tenant ID
4. **Premium SKU required**: Certificate was issued after June 2023

### Verification Commands
```powershell
# Check Azure login
az account show

# Check Key Vault access
az keyvault certificate show --vault-name "kv-infotek-signing-164" --name "infotek-codesigning-cert"

# Check role assignments
az role assignment list --scope "/subscriptions/a1373717-2e0b-40f1-a27d-537e4f1eaccc/resourceGroups/rg-infotek-appsigning/providers/Microsoft.KeyVault/vaults/kv-infotek-signing-164"
```

## 📋 AppSource Submission Checklist

### Before Submission
- [ ] All app files signed with Azure Key Vault certificate
- [ ] Certificate expires after app publication date (expires: 2026-04-30)
- [ ] Test signing verification completed
- [ ] App validation passed
- [ ] Documentation updated

### Signing Verification
```powershell
# Verify signature
Get-AuthenticodeSignature "signed-app-file.app"

# Check certificate details
(Get-AuthenticodeSignature "signed-app-file.app").SignerCertificate
```

## 🎯 Quick Actions

### Sign Latest App
```powershell
# Quick command to sign the latest version
sign code azure-key-vault --azure-key-vault-url "https://kv-infotek-signing-164.vault.azure.net/" --azure-key-vault-certificate "infotek-codesigning-cert" --description "Deferred Revenue/Expense Management App by İnfotek" --verbosity Information ".\Infotek Yazilim ve Donanim A.S._Deferred RevenueExpense Management by İnfotek_********.app"
```

### Sign All Apps
```powershell
# Sign all app files in the directory
Get-ChildItem -Filter "*.app" | ForEach-Object {
    sign code azure-key-vault --azure-key-vault-url "https://kv-infotek-signing-164.vault.azure.net/" --azure-key-vault-certificate "infotek-codesigning-cert" --description "Deferred Revenue/Expense Management App by İnfotek" --verbosity Information $_.FullName
}
```

## 📞 Support & Contacts

### Azure Resources
- **Portal**: https://portal.azure.com
- **Resource Group**: [rg-infotek-appsigning](https://portal.azure.com/#@infotekas.com.tr/resource/subscriptions/a1373717-2e0b-40f1-a27d-537e4f1eaccc/resourceGroups/rg-infotek-appsigning/overview)
- **Key Vault**: [kv-infotek-signing-164](https://portal.azure.com/#@infotekas.com.tr/resource/subscriptions/a1373717-2e0b-40f1-a27d-537e4f1eaccc/resourceGroups/rg-infotek-appsigning/providers/Microsoft.KeyVault/vaults/kv-infotek-signing-164/overview)

### Company Information
- **Website**: https://www.infotek.com.tr
- **Domain**: infotekas.com.tr
- **Legal Name**: İnfotek Yazılım ve Donanım A.Ş.

---

## 📝 Notes

- Setup completed: August 20, 2025
- Key Vault creation: Successful
- Certificate upload: Successful
- Test signing: ✅ Verified working
- Ready for AppSource submission: ✅

**Last Updated**: August 20, 2025  
**Status**: Production Ready 🚀