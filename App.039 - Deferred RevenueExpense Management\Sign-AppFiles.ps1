# AppSource App Signing Script
# This script signs .app files using Azure Key Vault
# 
# Prerequisites:
# 1. Azure Key Vault with code signing certificate
# 2. Proper access policies configured
# 3. Either managed identity or service principal credentials
#
# Usage:
# .\Sign-AppFiles.ps1 -KeyVaultUrl "https://YourKeyVault.vault.azure.net/" -CertificateName "YourCertName"

param(
    [Parameter(Mandatory=$true)]
    [string]$KeyVaultUrl,
    
    [Parameter(Mandatory=$true)]
    [string]$CertificateName,
    
    [Parameter(ParameterSetName="ServicePrincipal", Mandatory=$false)]
    [string]$ClientId,
    
    [Parameter(ParameterSetName="ServicePrincipal", Mandatory=$false)]
    [string]$ClientSecret,
    
    [Parameter(ParameterSetName="ServicePrincipal", Mandatory=$false)]
    [string]$TenantId,
    
    [Parameter(Mandatory=$false)]
    [switch]$UseManagedIdentity,
    
    [Parameter(Mandatory=$false)]
    [string]$Description = "Deferred Revenue/Expense Management App by İnfotek",
    
    [Parameter(Mandatory=$false)]
    [string]$DescriptionUrl = "https://www.infotek.com.tr",
    
    [Parameter(Mandatory=$false)]
    [string]$AppFilePattern = "*.app",
    
    [Parameter(Mandatory=$false)]
    [switch]$SignLatestOnly
)

# Set dotnet path
$dotnetPath = "C:\Program Files\dotnet\dotnet.exe"

# Get the script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

Write-Host "Starting app signing process..." -ForegroundColor Green
Write-Host "Script directory: $scriptDir" -ForegroundColor Yellow

# Find app files to sign
if ($SignLatestOnly) {
    # Find the latest app file by version
    $appFiles = Get-ChildItem -Path $scriptDir -Filter $AppFilePattern | 
                Sort-Object Name -Descending | 
                Select-Object -First 1
    Write-Host "Signing latest app file only: $($appFiles.Name)" -ForegroundColor Yellow
} else {
    $appFiles = Get-ChildItem -Path $scriptDir -Filter $AppFilePattern
    Write-Host "Found $($appFiles.Count) app files to sign:" -ForegroundColor Yellow
    $appFiles | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor Cyan }
}

if ($appFiles.Count -eq 0) {
    Write-Error "No app files found matching pattern: $AppFilePattern"
    exit 1
}

# Prepare signing command arguments
$baseArguments = @(
    "code", "azure-key-vault",
    "--azure-key-vault-url", $KeyVaultUrl,
    "--azure-key-vault-certificate", $CertificateName,
    "--description", $Description,
    "--description-url", $DescriptionUrl,
    "--verbosity", "Information"
)

# Add authentication method
if ($UseManagedIdentity) {
    $baseArguments += @("--azure-credential-type", "managed-identity")
    Write-Host "Using Managed Identity for authentication" -ForegroundColor Yellow
} elseif ($ClientId -and $ClientSecret -and $TenantId) {
    # For service principal, we need to set environment variables
    $env:AZURE_CLIENT_ID = $ClientId
    $env:AZURE_CLIENT_SECRET = $ClientSecret
    $env:AZURE_TENANT_ID = $TenantId
    $baseArguments += @("--azure-credential-type", "azure-cli")
    Write-Host "Using Service Principal for authentication" -ForegroundColor Yellow
} else {
    $baseArguments += @("--azure-credential-type", "azure-cli")
    Write-Host "Using default Azure authentication (Azure CLI)" -ForegroundColor Yellow
}

# Sign each app file
$successCount = 0
$failureCount = 0

foreach ($appFile in $appFiles) {
    Write-Host "`nSigning: $($appFile.Name)" -ForegroundColor Green
    
    $signArguments = $baseArguments + $appFile.FullName
    
    try {
        # Execute the signing command
        Write-Host "Executing: sign $($signArguments -join ' ')" -ForegroundColor Cyan
        
        $processArgs = @("tool", "run", "sign") + $signArguments
        $process = Start-Process -FilePath $dotnetPath -ArgumentList $processArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Host "✓ Successfully signed: $($appFile.Name)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Error "✗ Failed to sign: $($appFile.Name) (Exit code: $($process.ExitCode))"
            $failureCount++
        }
    }
    catch {
        Write-Error "✗ Error signing $($appFile.Name): $($_.Exception.Message)"
        $failureCount++
    }
}

# Summary
Write-Host "`n" + "="*50 -ForegroundColor Yellow
Write-Host "SIGNING SUMMARY" -ForegroundColor Yellow
Write-Host "="*50 -ForegroundColor Yellow
Write-Host "Successfully signed: $successCount files" -ForegroundColor Green
Write-Host "Failed to sign: $failureCount files" -ForegroundColor Red

if ($failureCount -gt 0) {
    Write-Host "`nSome files failed to sign. Please check the error messages above." -ForegroundColor Red
    exit 1
} else {
    Write-Host "`nAll files signed successfully!" -ForegroundColor Green
}