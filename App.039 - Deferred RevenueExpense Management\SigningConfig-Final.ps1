﻿# Azure Key Vault Configuration for İnfotek App Signing  
# Generated: 08/20/2025 11:25:45
# Subscription: MCPP Subscription
# Tenant: Infotek Yaz. ve Don. Hiz. A.S.

$KeyVaultUrl = "https://kv-infotek-signing-164.vault.azure.net/"
$CertificateName = "infotek-codesigning-cert"
$ClientId = "a4018b7f-fc13-4c32-90c4-c19fc3c83e1e"
$ClientSecret = "****************************************"
$TenantId = "d3dcd3d0-46e6-41be-8e63-c5fa8f564846"
$Description = "Deferred Revenue/Expense Management App by İnfotek"
$DescriptionUrl = "https://www.infotek.com.tr"
$SignLatestOnly = $true

# Usage example:
# .\Sign-AppFiles.ps1 -KeyVaultUrl $KeyVaultUrl -CertificateName $CertificateName -ClientId $ClientId -ClientSecret $ClientSecret -TenantId $TenantId
