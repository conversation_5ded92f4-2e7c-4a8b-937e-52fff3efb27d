# Simple App Signing Script for İnfotek
param(
    [Parameter(Mandatory=$true)]
    [string]$AppPath
)

Write-Host "=== İnfotek App Signing Tool ===" -ForegroundColor Cyan
Write-Host "App file: $AppPath" -ForegroundColor Yellow

if (-not (Test-Path $AppPath)) {
    Write-Host " ERROR: File not found: $AppPath" -ForegroundColor Red
    exit 1
}

# Create signed copy
$FileInfo = Get-Item $AppPath
$SignedFileName = $FileInfo.BaseName + ".signed" + $FileInfo.Extension
$SignedPath = Join-Path $FileInfo.DirectoryName $SignedFileName

Write-Host "Original: $($FileInfo.Name)" -ForegroundColor Cyan
Write-Host "Creating: $SignedFileName" -ForegroundColor Yellow

Copy-Item $AppPath $SignedPath -Force
Write-Host " Signed copy created" -ForegroundColor Green
Write-Host ""

# Sign with Azure Key Vault
Write-Host "=== Signing with Azure Key Vault ===" -ForegroundColor Cyan
sign code azure-key-vault --azure-key-vault-url "https://kv-infotek-signing-164.vault.azure.net/" --azure-key-vault-certificate "infotek-codesigning-cert" --description "Deferred Revenue/Expense Management App by İnfotek" --verbosity Information $SignedPath

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host " SUCCESS: App signed successfully!" -ForegroundColor Green
    Write-Host " Use the SIGNED copy for AppSource submission! " -ForegroundColor Green
} else {
    Write-Host " Signing failed!" -ForegroundColor Red
    Remove-Item $SignedPath -Force -ErrorAction SilentlyContinue
}
