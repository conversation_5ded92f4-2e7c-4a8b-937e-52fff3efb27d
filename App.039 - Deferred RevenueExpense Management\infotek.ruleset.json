{"name": "Infotek Yazilim ve Donanim A.S.", "description": "These rules must be respected by all the AL code written within the company.", "rules": [{"id": "LC0010", "action": "None", "justification": "Cyclomatic complexity and Maintainability index."}, {"id": "LC0023", "action": "None", "justification": "Fieldgroup {0} is missing on table {1}."}, {"id": "LC0068", "action": "None", "justification": "Informs the user that there are missing permission to access tabledata."}, {"id": "LC0084", "action": "None", "justification": "Database read methods, like Record.Get(), returns a boolean indicating whether the record was successfully retrieved. Failing to use this return value can lead to uncaught errors, poor error handling, and a lack of actionable feedback for users when something goes wrong."}, {"id": "LC0091", "action": "None", "justification": "Database read methods, like Record.Get(), returns a boolean indicating whether the record was successfully retrieved. Failing to use this return value can lead to uncaught errors, poor error handling, and a lack of actionable feedback for users when something goes wrong."}]}