codeunit 71019081 "DRE Integ. DRE INF"
{

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"App License Management INF", OnAfterAppLicenseProcessed, '', true, false)]
    local procedure OnAfterAppLicenseProcessed_InCU_AppLicenseManagement(InfotekAppsLicense: Record "Infotek Apps License INF")
    begin
        SynchronizeLicenseInformationAfterAppLicenseProcessed(InfotekAppsLicense)
    end;

    local procedure SynchronizeLicenseInformationAfterAppLicenseProcessed(InfotekAppsLicense: Record "Infotek Apps License INF")
    var
        DRELicInf: Record "DRE Lic. Inf. DRE INF";
        AppInfo: ModuleInfo;
    begin
        NavApp.GetCurrentModuleInfo(AppInfo);
        if InfotekAppsLicense."App ID" <> AppInfo.Id() then
            exit;

        DRELicInf.Get();
        DRELicInf."Trial Period" := InfotekAppsLicense."Trial Period";
        DRELicInf."Trial Period Start Date" := InfotekAppsLicense."Trial Period Start Date";
        DRELicInf."Warning 1 Date" := InfotekAppsLicense."Warning 1 Date";
        DRELicInf."License Expiration Date" := InfotekAppsLicense."License Expiration Date";
        DRELicInf."Warning 2 Date" := InfotekAppsLicense."Warning 2 Date";
        DRELicInf."Grace Expiration Date" := InfotekAppsLicense."Grace Expiration Date";
        DRELicInf.Modify(true);
    end;

    procedure CheckLicense()
    var
        AppInfo: ModuleInfo;
    begin
        NavApp.GetCurrentModuleInfo(AppInfo);
        SynchronizeLicense(AppInfo.Id(), AppInfo.Name());
        CheckLicenseValidity(AppInfo.Id());
    end;

    local procedure SynchronizeLicense(AppID: Guid; AppName: Text)
    var
        DRELicInf: Record "DRE Lic. Inf. DRE INF";
        InfotekAppsLicense: Record "Infotek Apps License INF";
    begin
        DRELicInf.Get();
        InfotekAppsLicense.SynchronizeLicense(AppID, CopyStr(AppName, 1, 50),
                                DRELicInf."Trial Period",
                                DRELicInf."Trial Period Start Date",
                                DRELicInf."License Expiration Date",
                                DRELicInf."Grace Expiration Date",
                                DRELicInf."Warning 1 Date",
                                DRELicInf."Warning 2 Date");
        DRELicInf.Modify(true);
    end;

    local procedure CheckLicenseValidity(AppID: Guid)
    var
        AppLicenseManagement: Codeunit "App License Management INF";
    begin
        AppLicenseManagement.CheckLicenseValidity(AppID);
    end;
}