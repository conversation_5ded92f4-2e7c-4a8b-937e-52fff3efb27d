codeunit 71019083 "Defer. Rev./Exp Events DRE INF"
{
    [EventSubscriber(ObjectType::Table, Database::"G/L Entry", OnAfterCopyGLEntryFromGenJnlLine, '', false, false)]
    local procedure OnAfterCopyGLEntryFromGenJnlLine(var GLEntry: Record "G/L Entry"; var GenJournalLine: Record "Gen. Journal Line")
    var
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
    begin
        GLEntry."Defer. RevExp Doc. No. DRE INF" := GenJournalLine."Defer. RevExp Doc. No. DRE INF";
        GLEntry."DeferRevExpDocLine No. DRE INF" := GenJournalLine."DeferRevExpDocLine No. DRE INF";

        if DeferRevExpLine.Get(GenJournalLine."Defer. RevExp Doc. No. DRE INF", GenJournalLine."DeferRevExpDocLine No. DRE INF") then begin
            DeferRevExpLine.Validate(Posted, true);
            DeferRevExpLine.Modify(true);
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Vendor Ledger Entry", OnAfterCopyVendLedgerEntryFromGenJnlLine, '', false, false)]
    local procedure OnAfterCopyVendLedgerEntryFromGenJnlLine(var VendorLedgerEntry: Record "Vendor Ledger Entry"; GenJournalLine: Record "Gen. Journal Line")
    begin
        VendorLedgerEntry."Defer. RevExp Doc. No. DRE INF" := GenJournalLine."Defer. RevExp Doc. No. DRE INF";
        VendorLedgerEntry."DeferRevExpDocLine No. DRE INF" := GenJournalLine."DeferRevExpDocLine No. DRE INF";
    end;

    [EventSubscriber(ObjectType::Table, Database::"Cust. Ledger Entry", OnAfterCopyCustLedgerEntryFromGenJnlLine, '', false, false)]
    local procedure OnAfterCopyCustLedgerEntryFromGenJnlLine(var CustLedgerEntry: Record "Cust. Ledger Entry"; GenJournalLine: Record "Gen. Journal Line")
    begin
        CustLedgerEntry."Defer. RevExp Doc. No. DRE INF" := GenJournalLine."Defer. RevExp Doc. No. DRE INF";
        CustLedgerEntry."DeferRevExpDocLine No. DRE INF" := GenJournalLine."DeferRevExpDocLine No. DRE INF";
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnAfterPostPurchaseDoc, '', false, false)]
    local procedure "Purch.-Post_OnAfterPostPurchaseDoc"(var PurchaseHeader: Record "Purchase Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; PurchRcpHdrNo: Code[20]; RetShptHdrNo: Code[20]; PurchInvHdrNo: Code[20]; PurchCrMemoHdrNo: Code[20]; CommitIsSupressed: Boolean)
    begin
        UpdatePostedDocumentNoOnDeferRevExpHeader(PurchaseHeader, PurchInvHdrNo, PurchCrMemoHdrNo);

        if PurchaseHeader."Defer Rev/Exp Doc. No. DRE INF" <> '' then
            DeferRevExpMngmt.MarkDeferLinesAsPosted(PurchaseHeader."Defer Rev/Exp Doc. No. DRE INF", PurchaseHeader."Posting Date");
    end;

    local procedure UpdatePostedDocumentNoOnDeferRevExpHeader(var PurchaseHeader: Record "Purchase Header"; PurchInvHdrNo: Code[20]; PurchCrMemoHdrNo: Code[20])
    var
        DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
    begin
        if not DeferRevExpHeader.Get(PurchaseHeader."Defer Rev/Exp Doc. No. DRE INF") then
            exit;

        if PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::Invoice then
            DeferRevExpHeader.Validate("Posted Document No.", PurchInvHdrNo)
        else
            if PurchaseHeader."Document Type" = PurchaseHeader."Document Type"::"Credit Memo" then
                DeferRevExpHeader.Validate("Posted Document No.", PurchCrMemoHdrNo);

        DeferRevExpHeader.Modify(false);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", OnAfterPostSalesDoc, '', false, false)]
    local procedure "Sales-Post_OnAfterPostSalesDoc"(var SalesHeader: Record "Sales Header"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line"; SalesShptHdrNo: Code[20]; RetRcpHdrNo: Code[20]; SalesInvHdrNo: Code[20]; SalesCrMemoHdrNo: Code[20]; CommitIsSuppressed: Boolean; InvtPickPutaway: Boolean; var CustLedgerEntry: Record "Cust. Ledger Entry"; WhseShip: Boolean)
    begin
        UpdatePostedDocumentNoOnDeferRevExpHeaderSales(SalesHeader, SalesInvHdrNo, SalesCrMemoHdrNo);

        if SalesHeader."Defer Rev/Exp Doc. No. DRE INF" <> '' then begin
            DeferRevExpMngmt.MarkDeferLinesAsPosted(SalesHeader."Defer Rev/Exp Doc. No. DRE INF", SalesHeader."Posting Date");
            DeferRevExpMngmt.UpdateDeferRevExpHeaderAfterPosting(SalesHeader."Defer Rev/Exp Doc. No. DRE INF",
                                                                    SalesHeader."Posting Date",
                                                                    SalesHeader."External Document No.",
                                                                    SalesHeader."Currency Factor",
                                                                    SalesHeader."Posting Description");
        end;
    end;

    local procedure UpdatePostedDocumentNoOnDeferRevExpHeaderSales(var SalesHeader: Record "Sales Header"; SalesInvHdrNo: Code[20]; SalesCrMemoHdrNo: Code[20])
    var
        DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
    begin
        if not DeferRevExpHeader.Get(SalesHeader."Defer Rev/Exp Doc. No. DRE INF") then
            exit;

        if SalesHeader."Document Type" = SalesHeader."Document Type"::Invoice then
            DeferRevExpHeader.Validate("Posted Document No.", SalesInvHdrNo)
        else
            if SalesHeader."Document Type" = SalesHeader."Document Type"::"Credit Memo" then
                DeferRevExpHeader.Validate("Posted Document No.", SalesCrMemoHdrNo);

        DeferRevExpHeader.Modify(false);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Batch", OnAfterPostGenJnlLine, '', false, false)]
    local procedure OnAfterPostGenJnlLine(var GenJournalLine: Record "Gen. Journal Line")
    begin
        if GenJournalLine."Defer. RevExp Doc. No. DRE INF" <> '' then
            DeferRevExpMngmt.MarkDeferLinesAsPosted(GenJournalLine."Defer. RevExp Doc. No. DRE INF", GenJournalLine."Posting Date");
    end;

    local procedure UpdatePostedFieldOnPostingPurhaseInvoice(var PurchaseHeader: Record "Purchase Header"; PurchaseLine: Record "Purchase Line")
    var
        DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
    begin
        if PurchaseHeader."Defer Rev/Exp Doc. No. DRE INF" = '' then
            exit;

        if not DeferRevExpHeader.Get(PurchaseHeader."Defer Rev/Exp Doc. No. DRE INF") then
            exit;

        if PurchaseLine."No." <> DeferRevExpHeader."Rev./Exp. Reflection Account" then
            exit;

        DeferRevExpLine.SetRange("Document No.", PurchaseHeader."Defer Rev/Exp Doc. No. DRE INF");
        if not DeferRevExpLine.FindFirst() then
            exit;

        DeferRevExpLine.Posted := true;
        DeferRevExpLine.Modify(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnAfterPostPurchLine, '', false, false)]
    local procedure "Purch.-Post_OnAfterPostPurchLine"(var PurchaseHeader: Record "Purchase Header"; var PurchaseLine: Record "Purchase Line"; CommitIsSupressed: Boolean; var PurchInvLine: Record "Purch. Inv. Line"; var PurchCrMemoLine: Record "Purch. Cr. Memo Line"; var PurchInvHeader: Record "Purch. Inv. Header"; var PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr."; var PurchLineACY: Record "Purchase Line"; GenJnlLineDocType: Enum "Gen. Journal Document Type"; GenJnlLineDocNo: Code[20]; GenJnlLineExtDocNo: Code[35]; SrcCode: Code[10]; xPurchaseLine: Record "Purchase Line")
    begin
        UpdatePostedFieldOnPostingPurhaseInvoice(PurchaseHeader, PurchaseLine);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Reverse", OnReverseGLEntryOnAfterInsertGLEntry, '', false, false)]
    local procedure "Gen. Jnl.-Post Reverse_OnReverseGLEntryOnAfterInsertGLEntry"(var GLEntry: Record "G/L Entry"; GenJnlLine: Record "Gen. Journal Line"; GLEntry2: Record "G/L Entry"; var GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line")
    var
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
    begin
        if not DeferRevExpLine.Get(GLEntry."Defer. RevExp Doc. No. DRE INF", GLEntry."DeferRevExpDocLine No. DRE INF") then
            exit;

        DeferRevExpLine.Posted := false;
        DeferRevExpLine.Modify(true);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Cancelled Document", OnAfterInsertEvent, '', true, true)]
    local procedure OnAfterInsert_CancelledDocument(var Rec: Record "Cancelled Document"; RunTrigger: Boolean)
    begin
        ClearDeferRevExpHeaderOnCancellationPostedDocNo(Rec);
    end;

    local procedure ClearDeferRevExpHeaderOnCancellationPostedDocNo(Rec: Record "Cancelled Document")
    var
        PurchInvHeader: Record "Purch. Inv. Header";
        PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr.";
        SalesInvHeader: Record "Sales Invoice Header";
        SalesCrMemoHeader: Record "Sales Cr.Memo Header";
        DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
    begin
        case Rec."Source ID" of
            112:
                begin
                    SalesInvHeader.Get(Rec."Cancelled Doc. No.");
                    if not DeferRevExpHeader.Get(SalesInvHeader."Defer Rev/Exp Doc. No. DRE INF") then
                        exit;

                    DeferRevExpHeader."Posted Document No." := '';
                    DeferRevExpHeader.Modify(true);
                end;
            114:
                begin
                    SalesCrMemoHeader.Get(Rec."Cancelled Doc. No.");
                    if not DeferRevExpHeader.Get(SalesCrMemoHeader."Defer Rev/Exp Doc. No. DRE INF") then
                        exit;

                    DeferRevExpHeader."Posted Document No." := '';
                    DeferRevExpHeader.Modify(true);
                end;
            122:
                begin
                    PurchInvHeader.Get(Rec."Cancelled Doc. No.");
                    if not DeferRevExpHeader.Get(PurchInvHeader."Defer Rev/Exp Doc. No. DRE INF") then
                        exit;

                    DeferRevExpHeader."Posted Document No." := '';
                    DeferRevExpHeader.Modify(true);
                end;
            124:
                begin
                    PurchCrMemoHdr.Get(Rec."Cancelled Doc. No.");
                    if not DeferRevExpHeader.Get(PurchCrMemoHdr."Defer Rev/Exp Doc. No. DRE INF") then
                        exit;

                    DeferRevExpHeader."Posted Document No." := '';
                    DeferRevExpHeader.Modify(true);
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Alloc. Account Distribution", OnBeforeModifyEvent, '', true, true)]
    local procedure OnBeforeModifyEvent_Table_AllocAccountDistribution(var Rec: Record "Alloc. Account Distribution"; var xRec: Record "Alloc. Account Distribution"; RunTrigger: Boolean)
    begin
        DeferRevExpMngmt.CheckIfAllocationAccountIsUsedInDeferredRevExp(Rec."Allocation Account No.");
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Journal Alloc. Acc. Mgt.", OnBeforeCreateGeneralJournalLine, '', false, false)]
    // local procedure "Gen. Journal Alloc. Acc. Mgt._OnBeforeCreateGeneralJournalLine"(var GenJournalLine: Record "Gen. Journal Line"; var AllocationLine: Record "Allocation Line" temporary; var AllocationAccountGenJournalLine: Record "Gen. Journal Line")
    // begin
    //     DeferRevExpMngmt.UpdateRelevantFieldAfterGenerateGenJnlLinesFromAllocation(GenJournalLine);
    // end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Header", OnAfterDeleteEvent, '', false, false)]
    local procedure OnAfterDeleteEvent_PurchaseHeader(var Rec: Record "Purchase Header"; RunTrigger: Boolean)
    var
        DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
    begin
        if Rec."Defer Rev/Exp Doc. No. DRE INF" = '' then
            exit;

        if DeferRevExpHeader.Get(Rec."Defer Rev/Exp Doc. No. DRE INF") then
            if DeferRevExpHeader."Created Document No." = Rec."No." then begin
                DeferRevExpHeader."Created Document No." := '';
                DeferRevExpHeader.Modify(false);
            end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Header", OnAfterDeleteEvent, '', false, false)]
    local procedure OnAfterDeleteEvent_SalesHeader(var Rec: Record "Sales Header"; RunTrigger: Boolean)
    var
        DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
    begin
        if Rec."Defer Rev/Exp Doc. No. DRE INF" = '' then
            exit;

        if DeferRevExpHeader.Get(Rec."Defer Rev/Exp Doc. No. DRE INF") then
            if DeferRevExpHeader."Created Document No." = Rec."No." then begin
                DeferRevExpHeader."Created Document No." := '';
                DeferRevExpHeader.Modify(false);
            end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Correct Posted Sales Invoice", OnAfterCreateCorrectiveSalesCrMemo, '', false, false)]
    local procedure "Correct Posted Sales Invoice_OnAfterCreateCorrectiveSalesCrMemo"(SalesInvoiceHeader: Record "Sales Invoice Header"; var SalesHeader: Record "Sales Header"; var CancellingOnly: Boolean)
    begin
        // When a sales invoice is cancelled, unmark the posted deferred lines
        if SalesInvoiceHeader."Defer Rev/Exp Doc. No. DRE INF" <> '' then
            DeferRevExpMngmt.UnmarkDeferLinesAsPosted(SalesInvoiceHeader."Defer Rev/Exp Doc. No. DRE INF", SalesInvoiceHeader."Posting Date");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Correct Posted Purch. Invoice", OnAfterCreateCorrectivePurchCrMemo, '', false, false)]
    local procedure "Correct Posted Purch. Invoice_OnAfterCreateCorrectivePurchCrMemo"(PurchInvHeader: Record "Purch. Inv. Header"; var PurchaseHeader: Record "Purchase Header"; var CancellingOnly: Boolean; var SuppressCommit: Boolean)
    begin
        // When a purchase invoice is cancelled, unmark the posted deferred lines
        if PurchInvHeader."Defer Rev/Exp Doc. No. DRE INF" <> '' then
            DeferRevExpMngmt.UnmarkDeferLinesAsPosted(PurchInvHeader."Defer Rev/Exp Doc. No. DRE INF", PurchInvHeader."Posting Date");
    end;



    var
        DeferRevExpMngmt: Codeunit "Defer. Rev/Exp. Mngmt. DRE INF";
}