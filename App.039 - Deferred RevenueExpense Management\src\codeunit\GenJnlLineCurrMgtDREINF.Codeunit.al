codeunit 71019085 "Gen. Jnl Line Curr Mgt DRE INF"
{
    SingleInstance = true;

    var
        StoredRecordID: RecordId;
        StoredCurrencyFactor: Decimal;
        HasStoredValue: Boolean;
        CurrencyExchangeRateChangedMsg: Label 'The currency exchange rate has changed from %1 to %2 due to the posting date change. Do you want to keep the new exchange rate?', Comment = '%1 = old exchange rate, %2 = new exchange rate';
        CurrencyExchangeRateRestoredMsg: Label 'The currency exchange rate has been restored to %1.', Comment = '%1 = restored exchange rate';

    [EventSubscriber(ObjectType::Table, Database::"Gen. Journal Line", OnBeforeValidateEvent, "Posting Date", false, false)]
    local procedure OnBeforeValidatePostingDate(var Rec: Record "Gen. Journal Line"; var xRec: Record "Gen. Journal Line")
    begin
        // Store the current currency factor before posting date validation
        if (Rec."Currency Code" <> '') and (Rec."Currency Factor" <> 0) then begin
            StoredCurrencyFactor := Rec."Currency Factor";
            StoredRecordID := Rec.RecordId();
            HasStoredValue := true;
        end else
            HasStoredValue := false;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Gen. Journal Line", OnAfterValidateEvent, "Posting Date", false, false)]
    local procedure OnAfterValidatePostingDate(var Rec: Record "Gen. Journal Line"; var xRec: Record "Gen. Journal Line")
    var
        ConfirmManagement: Codeunit "Confirm Management";
        NewCurrencyFactor: Decimal;
        StoredExchangeRate: Decimal;
        NewExchangeRate: Decimal;
        UserResponse: Boolean;
    begin
        // Check if we have a stored value and if it's for the same record
        if not HasStoredValue or (Rec.RecordId() <> StoredRecordID) then
            exit;

        // Check if currency factor has changed after posting date validation
        NewCurrencyFactor := Rec."Currency Factor";

        if (NewCurrencyFactor <> 0) and (StoredCurrencyFactor <> 0) and
           (Abs(NewCurrencyFactor - StoredCurrencyFactor) > 0.00001) then begin

            // Calculate exchange rates (1/Currency Factor) for user display
            StoredExchangeRate := 1 / StoredCurrencyFactor;
            NewExchangeRate := 1 / NewCurrencyFactor;

            // Ask user for confirmation to keep the changed currency factor
            UserResponse := ConfirmManagement.GetResponse(
                StrSubstNo(CurrencyExchangeRateChangedMsg, StoredExchangeRate, NewExchangeRate),
                true); // Default to Yes

            if not UserResponse then begin
                // Restore the old currency factor
                Rec.Validate("Currency Factor", StoredCurrencyFactor);
                Message(CurrencyExchangeRateRestoredMsg, StoredExchangeRate);
            end;
        end;

        // Clear stored values
        ClearStoredValues();
    end;

    local procedure ClearStoredValues()
    begin
        StoredCurrencyFactor := 0;
        Clear(StoredRecordID);
        HasStoredValue := false;
    end;
}