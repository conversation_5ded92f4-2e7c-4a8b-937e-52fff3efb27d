codeunit 71019080 "InstallDeferredRevExp DRE INF"
{
    Subtype = Install;
    Access = Internal;

    trigger OnInstallAppPerDatabase()
    begin
        RegisterMe();
    end;

    procedure RegisterMe()
    var
        InfotekAppsLicense: Record "Infotek Apps License INF";
        DRELicInf: Record "DRE Lic. Inf. DRE INF";
        AppInfo: ModuleInfo;
    begin
        if not DRELicInf.Get() then begin
            DRELicInf.Init();
            DRELicInf.Insert(true);
            DRELicInf.Get();
        end;
        NavApp.GetCurrentModuleInfo(AppInfo);
        InfotekAppsLicense.RegisterAppLicense(AppInfo.Id(),
                                            CopyStr(AppInfo.Name(), 1, 50),
                                            DRELicInf."Trial Period",
                                            DRELicInf."Trial Period Start Date",
                                            DRELicInf."License Expiration Date",
                                            DRELicInf."Grace Expiration Date",
                                            DRELicInf."Warning 1 Date",
                                            DRELicInf."Warning 2 Date");
        DRELicInf.Modify(true)
    end;
}