page 71019084 "CreateMonthlyJnlVouch. DRE INF"
{
    ApplicationArea = All;
    Caption = 'Create Monthly Journal Voucher';
    PageType = StandardDialog;
    Extensible = false;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field(StartingDate; StartingDate)
                {
                    Caption = 'Starting Date';
                    ToolTip = 'Specifies the value of the Starting Date field.';
                    ShowMandatory = true;
                    trigger OnValidate()
                    begin
                        EndingDate := CalcDate('<1M - 1D>', StartingDate);
                    end;
                }
                field(EndingDate; EndingDate)
                {
                    Caption = 'Ending Date';
                    ToolTip = 'Specifies the value of the Ending Date field.';
                    ShowMandatory = true;
                }
            }
        }
    }
    trigger OnQueryClosePage(CloseAction: Action): Boolean
    var
        ValidDateErr: Label 'Please enter a valid date range.';
    begin
        if CloseAction <> CloseAction::OK then
            exit;

        if (StartingDate = 0D) or (EndingDate = 0D) then
            Error(ValidDateErr);

        DeferRevExpMngmt.CreateMonthlyJournalVouchers(StartingDate, EndingDate);
    end;

    var
        DeferRevExpMngmt: Codeunit "Defer. Rev/Exp. Mngmt. DRE INF";
        StartingDate: Date;
        EndingDate: Date;
}