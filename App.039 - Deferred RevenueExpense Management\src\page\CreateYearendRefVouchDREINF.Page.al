page 71019085 "CreateYearendRef.Vouch DRE INF"
{
    ApplicationArea = All;
    Caption = 'Create Year-end Reflection Voucher';
    PageType = StandardDialog;
    Extensible = false;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field(YearendReflectionVoucherPostingDate; YearendReflectionVoucherPostingDate)
                {
                    Caption = 'Year-end Reflection Voucher Posting Date';
                    ToolTip = 'Specifies the value of the Year-end Reflection Voucher Posting Date field.';
                    ShowMandatory = true;

                    trigger OnValidate()
                    var
                        PostingDateUpdatedMsg: Label 'The Year-end Reflection Voucher Posting Date has been changed to the last day of the current year.';
                    begin
                        if YearendReflectionVoucherPostingDate <> CalcDate('<CY>', YearendReflectionVoucherPostingDate) then begin
                            YearendReflectionVoucherPostingDate := CalcDate('<CY>', YearendReflectionVoucherPostingDate);
                            Message(PostingDateUpdatedMsg);
                        end;
                    end;
                }
            }
        }

    }
    trigger OnQueryClosePage(CloseAction: Action): Boolean
    var
    //ValidDateErr: Label 'Please enter a valid date range.';
    begin
        if CloseAction <> CloseAction::OK then
            exit;

        DeferRevExpMngmt.CreateYearEndReflectionVoucher(YearendReflectionVoucherPostingDate);
    end;

    var
        DeferRevExpMngmt: Codeunit "Defer. Rev/Exp. Mngmt. DRE INF";
        YearendReflectionVoucherPostingDate: Date;
}