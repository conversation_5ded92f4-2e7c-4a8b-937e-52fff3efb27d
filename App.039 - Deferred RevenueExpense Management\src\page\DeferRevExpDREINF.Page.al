page 71019081 "Defer. Rev/Exp DRE INF"
{
    ApplicationArea = All;
    Caption = 'Deferred Revenue/Expense Document';
    PageType = Document;
    SourceTable = "Defer. Rev/Exp Header DRE INF";
    DelayedInsert = true;
    Extensible = false;
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                Editable = Rec.Status = Rec.Status::Open;
                field("Defer. Rev/Exp Code"; Rec."Defer. Rev/Exp Code")
                {
                }
                field("Source Document No."; Rec."Source Document No.")
                {
                    trigger OnDrillDown()
                    var
                        SourceDocument: Record "Defer. Rev/Exp Header DRE INF";
                    begin
                        SourceDocument.Get(Rec."Source Document No.");
                        PageManagement.PageRun(SourceDocument);
                    end;
                }
                field("No."; Rec."No.")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field(Type; Rec."Type")
                {
                }
                field("Future Months Account Type"; Rec."Future Months Account Type")
                {
                }
                field("Rev./Exp. Acc. - Future Months"; Rec."Rev./Exp. Acc. - Future Months")
                {
                }
                field("Future Years Account Type"; Rec."Future Years Account Type")
                {
                }
                field("Rev./Exp. Acc. - Future Years"; Rec."Rev./Exp. Acc. - Future Years")
                {
                }
                field("Reflection Account Type"; Rec."Reflection Account Type")
                {
                }
                field("Rev./Exp. Reflection Account"; Rec."Rev./Exp. Reflection Account")
                {
                }
                field("Calculation Method"; Rec."Calculation Method")
                {
                }
                field("Starting Date"; Rec."Starting Date")
                {
                }
                field("Ending Date"; Rec."Ending Date")
                {
                }
                field("Number of Periods"; Rec."No. of Periods")
                {
                    Editable = false;
                }
                field("GIB Document Type"; Rec."GIB Document Type")
                {
                }
                field("GIB Document Description"; Rec."GIB Document Description")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Document No."; Rec."External Document No.")
                {
                }
                // field("Document Description"; Rec."Document Description")
                // {
                //     ToolTip = 'Specifies the value of the Document Description field.';
                // }
                field("Source No."; Rec."Source No.")
                {
                }
                field("Source Name"; Rec."Source Name")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                    trigger OnAssistEdit()
                    // var
                    //     IsHandled: Boolean;
                    begin
                        // IsHandled := false;
                        // OnBeforeCurrencyCodeOnAssistEdit(Rec, xRec, IsHandled);
                        // if IsHandled then
                        //     exit;

                        Clear(ChangeExchangeRate);
                        if Rec."Posting Date" <> 0D then
                            ChangeExchangeRate.SetParameter(Rec."Currency Code", Rec."Currency Factor", Rec."Posting Date")
                        else
                            ChangeExchangeRate.SetParameter(Rec."Currency Code", Rec."Currency Factor", WorkDate());
                        if ChangeExchangeRate.RunModal() = Action::OK then
                            Rec.Validate("Currency Factor", ChangeExchangeRate.GetParameter());

                        Clear(ChangeExchangeRate);
                    end;
                }
                field("Document Type"; Rec."Document Type")
                {
                }
                field("Create Invoice"; Rec."Create Invoice")
                {
                    ApplicationArea = All;
                    Visible = false;
                    ObsoleteState = Pending;
                    ObsoleteReason = 'Replaced by the Document Type field';
                    ObsoleteTag = '22.0';
                }
                field("Use Last Day of the Month"; Rec."Use Last Day of the Month")
                {
                }
                field("Initial Amount to Defer"; Rec."Initial Amount to Defer")
                {
                    Editable = Rec."Deferred Amount" = 0;
                }
                field("Total Line Amount"; Rec."Total Line Amount")
                {
                }
                field("Deferred Amount"; Rec."Deferred Amount")
                {
                }
                field("Remaining Amount to Defer"; Rec."Initial Amount to Defer" - Rec."Deferred Amount")
                {
                    Caption = 'Remaining Amount to Defer';
                    ToolTip = 'Specifies the value of the Remaining Amount to Defer field.';
                }
                field("Posting Description"; Rec."Posting Description")
                {
                }
                field("Shortcut Dimension 1 Code"; Rec."Shortcut Dimension 1 Code")
                {
                }
                field("Shortcut Dimension 2 Code"; Rec."Shortcut Dimension 2 Code")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field(Closed; Rec.Closed)
                {
                }
                field("Posted Document No."; Rec."Posted Document No.")
                {
                    trigger OnDrillDown()
                    var
                        SalesCrMemoHeader: Record "Sales Cr.Memo Header";
                        SalesInvoiceHeader: Record "Sales Invoice Header";
                        PurchInvHeader: Record "Purch. Inv. Header";
                        PurchCrMemoHdr: Record "Purch. Cr. Memo Hdr.";
                    begin
                        if Rec.Type = Rec.Type::Revenue then
                            if Rec."Initial Amount to Defer" < 0 then begin
                                SalesCrMemoHeader.Get(Rec."Posted Document No.");
                                PageManagement.PageRun(SalesCrMemoHeader);
                            end
                            else begin
                                SalesInvoiceHeader.Get(Rec."Posted Document No.");
                                PageManagement.PageRun(SalesInvoiceHeader);
                            end
                        else
                            if Rec.Type = Rec.Type::Expense then
                                if Rec."Initial Amount to Defer" < 0 then begin
                                    PurchCrMemoHdr.Get(Rec."Posted Document No.");
                                    PageManagement.PageRun(PurchCrMemoHdr);
                                end
                                else begin
                                    PurchInvHeader.Get(Rec."Posted Document No.");
                                    PageManagement.PageRun(PurchInvHeader);
                                end;
                    end;
                }
                field("Created Document No."; Rec."Created Document No.")
                {
                    trigger OnDrillDown()
                    var
                        SalesHeader: Record "Sales Header";
                        PurchaseHeader: Record "Purchase Header";
                    begin
                        if Rec."Created Document No." = '' then
                            exit;

                        if Rec.Type = Rec.Type::Revenue then begin
                            SalesHeader.SetRange("Defer Rev/Exp Doc. No. DRE INF", Rec."No.");
                            if SalesHeader.FindFirst() then
                                PageManagement.PageRun(SalesHeader);
                        end else
                            if Rec.Type = Rec.Type::Expense then begin
                                PurchaseHeader.SetRange("Defer Rev/Exp Doc. No. DRE INF", Rec."No.");
                                if PurchaseHeader.FindFirst() then
                                    PageManagement.PageRun(PurchaseHeader);
                            end;
                    end;
                }
            }
            part(Lines; "Defer. Rev/Exp Subp. DRE INF")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            // action(CreateMonthlyJournalVoucherForThisDocument)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Create Monthly Journal Voucher for This Document';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     ToolTip = 'Create a monthly journal voucher for this document.';
            //     PromotedOnly = true;
            //     PromotedIsBig = true;
            //     Image = RefreshVoucher;
            //     Visible = (Rec.Status = Rec.Status::Released) and (Rec."Posted Document No." = '');
            //     trigger OnAction()
            //     var
            //         StartDate: Date;
            //         EndDate: Date;
            //     begin
            //         // Use the document's Starting Date and Ending Date fields
            //         StartDate := Rec."Starting Date";
            //         EndDate := Rec."Ending Date";
            //         if (StartDate = 0D) or (EndDate = 0D) then
            //             Error('Please enter valid Starting Date and Ending Date for this document.');
            //         // Only process this document's lines
            //         DeferRevExpMngmt.CreateMonthlyJournalVouchersForDocument(Rec, StartDate, EndDate);
            //     end;
            // }
            action(Release)
            {
                ApplicationArea = All;
                Caption = 'Release';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Release the document.';
                PromotedOnly = true;
                PromotedIsBig = true;
                Image = ReleaseDoc;
                Visible = Rec.Status = Rec.Status::Open;
                trigger OnAction()
                begin
                    Rec.Validate(Status, Rec.Status::Released);
                end;
            }
            action(Reopen)
            {
                ApplicationArea = All;
                Caption = 'Reopen';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Reopen the document.';
                PromotedOnly = true;
                PromotedIsBig = true;
                Image = ReOpen;
                Visible = Rec.Status = Rec.Status::Released;
                trigger OnAction()
                begin
                    Rec.Validate(Status, Rec.Status::Open);
                end;
            }
            action(CalculateLines)
            {
                ApplicationArea = All;
                Caption = 'Calculate Lines';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Calculate the lines.';
                PromotedOnly = true;
                PromotedIsBig = true;
                Image = Calculate;
                //Visible = Rec.Status = Rec.Status::Open;
                trigger OnAction()
                begin
                    DeferRevExpMngmt.CalculateLines(Rec);
                    //InsertLines(Rec."Starting Date", Rec."No. of Periods", Rec."Initial Amount to Defer");
                end;
            }
            action(CreateJournalVoucher)
            {
                ApplicationArea = All;
                Caption = 'Create Journal Voucher/Invoice';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Create a journal voucher.';
                PromotedOnly = true;
                PromotedIsBig = true;
                Image = TransferToGeneralJournal;
                Visible = (Rec.Status = Rec.Status::Released) and (Rec."Posted Document No." = '');
                trigger OnAction()
                begin
                    DeferRevExpMngmt.CreateJournalVoucherFromRevExpHeader(Rec);
                end;
            }
            action(Close)
            {
                ApplicationArea = All;
                Caption = 'Close';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Close the document.';
                PromotedOnly = true;
                PromotedIsBig = true;
                Image = CompleteLine;
                Visible = Rec.Status = Rec.Status::Released;
                trigger OnAction()
                begin
                    DeferRevExpMngmt.Close(Rec);
                end;
            }
            action(CreateRelatedDocument)
            {
                ApplicationArea = All;
                Caption = 'Create Related Document';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Relationship;
                ToolTip = 'Executes the Create Related Document action.';
                Visible = (Rec."Source Document No." = '') and (Rec.Status = Rec.Status::Released);
                trigger OnAction()
                begin
                    DeferRevExpMngmt.CreateRelatedDocument(Rec, false);
                end;
            }
            action(RelatedDocuments)
            {
                ApplicationArea = All;
                Caption = 'Related Documents';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = RelatedInformation;
                RunObject = page "Defer. Rev/Exp List DRE INF";
                RunPageLink = "Source Document No." = field("No.");
                ToolTip = 'Executes the Related Documents action.';
                Visible = Rec."Source Document No." = '';
            }
            action(ClearClosed)
            {
                ApplicationArea = All;
                Caption = 'Clear Closed';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Clear the Closed field.';
                PromotedOnly = true;
                PromotedIsBig = true;
                Image = ClearLog;
                Visible = false;
                trigger OnAction()
                begin
                    Rec.Closed := false;
                    Rec.Modify(false);
                    CurrPage.Update();
                end;
            }
            action(Dimensions)
            {
                AccessByPermission = tabledata Dimension = R;
                ApplicationArea = Dimensions;
                Caption = 'Dimensions';
                Image = Dimensions;
                ShortcutKey = 'Alt+D';
                ToolTip = 'View or edit dimensions, such as area, project, or department, that you can assign to sales and purchase documents to distribute costs and analyze transaction history.';

                trigger OnAction()
                begin
                    Rec.ShowDimensions();
                    CurrPage.SaveRecord();
                end;
            }
        }
    }


    var
        DeferRevExpMngmt: Codeunit "Defer. Rev/Exp. Mngmt. DRE INF";
        PageManagement: Codeunit "Page Management";
        ChangeExchangeRate: Page "Change Exchange Rate";
}