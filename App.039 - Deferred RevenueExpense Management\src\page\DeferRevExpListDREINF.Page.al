page 71019082 "Defer. Rev/Exp List DRE INF"
{
    ApplicationArea = All;
    Caption = 'Deferred Revenue/Expense Documents';
    PageType = List;
    SourceTable = "Defer. Rev/Exp Header DRE INF";
    UsageCategory = Documents;
    CardPageId = "Defer. Rev/Exp DRE INF";
    Editable = false;
    Extensible = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Defer. Rev/Exp Code"; Rec."Defer. Rev/Exp Code")
                {
                }
                field(Type; Rec."Type")
                {
                }
                field("Rev./Exp. Acc. - Future Months"; Rec."Rev./Exp. Acc. - Future Months")
                {
                }
                field("Rev./Exp. Acc. - Future Years"; Rec."Rev./Exp. Acc. - Future Years")
                {
                }
                field("Rev./Exp. Reflection Account"; Rec."Rev./Exp. Reflection Account")
                {
                }
                field("Calculation Method"; Rec."Calculation Method")
                {
                }
                field("Number of Periods"; Rec."No. of Periods")
                {
                }
                field("GIB Document Type"; Rec."GIB Document Type")
                {
                }
                field("GIB Document Description"; Rec."GIB Document Description")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Document No."; Rec."External Document No.")
                {
                }
                // field("Document Description"; Rec."Document Description")
                // {
                //     ToolTip = 'Specifies the value of the Document Description field.';
                // }
                field("Source No."; Rec."Source No.")
                {
                }
                field("Source Name"; Rec."Source Name")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("Initial Amount to Defer"; Rec."Initial Amount to Defer")
                {
                }
                field("Initial Amount to Defer (LCY)"; DeferRevExpMngmt.CalculateLCYAmount(Rec."Initial Amount to Defer", Rec."Currency Factor"))
                {
                    Caption = 'Initial Amount to Defer (LCY)';
                    ToolTip = 'Specifies the value of the Initial Amount to Defer (LCY) field.';
                }
                field("Deferred Amount"; Rec."Deferred Amount")
                {
                }
                field("Deferred Amount (LCY)"; DeferRevExpMngmt.CalculateLCYAmount(Rec."Deferred Amount", Rec."Currency Factor"))
                {
                    Caption = 'Deferred Amount (LCY)';
                    ToolTip = 'Specifies the value of the Deferred Amount (LCY) field.';
                }
                field("Remaining Amount to Defer"; Rec."Initial Amount to Defer" - Rec."Deferred Amount")
                {
                    Caption = 'Remaining Amount to Defer';
                    ToolTip = 'Specifies the value of the Remaining Amount to Defer field.';
                }
                field("Remaining Amount to Defer (LCY)"; DeferRevExpMngmt.CalculateLCYAmount(Rec."Initial Amount to Defer" - Rec."Deferred Amount", Rec."Currency Factor"))
                {
                    Caption = 'Remaining Amount to Defer (LCY)';
                    ToolTip = 'Specifies the value of the Remaining Amount to Defer (LCY) field.';
                }
                field("Posting Description"; Rec."Posting Description")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field(Closed; Rec.Closed)
                {
                }

            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreateMonthlyJournalVoucher)
            {
                ApplicationArea = All;
                Caption = 'Create Monthly Journal Voucher';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Create a monthly journal voucher.';
                PromotedOnly = true;
                PromotedIsBig = true;
                Image = RefreshVoucher;
                trigger OnAction()
                begin
                    Page.RunModal(Page::"CreateMonthlyJnlVouch. DRE INF")
                end;
            }
            action(CreateYearEndReflectionVoucher)
            {
                ApplicationArea = All;
                Caption = 'Create Year-end Reflection Voucher';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Create Year-end Reflection Voucher the document.';
                PromotedOnly = true;
                PromotedIsBig = true;
                Image = CloseYear;
                trigger OnAction()
                begin
                    Page.RunModal(Page::"CreateYearendRef.Vouch DRE INF");
                end;
            }
        }
    }


    trigger OnOpenPage()
    var
        DREInteg: Codeunit "DRE Integ. DRE INF";
    begin
        DREInteg.CheckLicense();
    end;

    var
        DeferRevExpMngmt: Codeunit "Defer. Rev/Exp. Mngmt. DRE INF";
}