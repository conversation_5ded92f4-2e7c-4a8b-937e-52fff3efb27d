page 71019080 "Defer. Rev/Exp Setup DRE INF"
{
    ApplicationArea = All;
    Caption = 'Deferred Revenue/Expense Setup';
    PageType = List;
    SourceTable = "Defer. Rev/Exp Setup DRE INF";
    UsageCategory = Administration;
    Extensible = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(Code; Rec.Code)
                {
                }
                // field("Deferral Code"; Rec."Deferral Code")
                // {
                //     ToolTip = 'Specifies the value of the Deferral Code field.';
                // }
                field(Description; Rec.Description)
                {
                }
                field(Type; Rec."Type")
                {
                }
                field("Future Months Account Type"; Rec."Future Months Account Type")
                {
                }
                field("Rev./Exp. Acc. - Future Months"; Rec."Rev./Exp. Acc. - Future Months")
                {
                }
                field("Future Years Account Type"; Rec."Future Years Account Type")
                {
                }
                field("Rev./Exp. Acc. - Future Years"; Rec."Rev./Exp. Acc. - Future Years")
                {
                }
                field("Reflection Account Type"; Rec."Reflection Account Type")
                {
                }
                field("Rev./Exp. Reflection Account"; Rec."Rev./Exp. Reflection Account")
                {
                }
                field("Calculation Method"; Rec."Calculation Method")
                {
                }
                field("Number of Periods"; Rec."Number of Periods")
                {
                }
                field("GIB Document Type"; Rec."GIB Document Type")
                {
                }
                field("GIB Document Description"; Rec."GIB Document Description")
                {
                }
                field("No. Series"; Rec."No. Series")
                {
                }
                field("Journal Template Name"; Rec."Journal Template Name")
                {
                }
                field("Journal Batch Name"; Rec."Journal Batch Name")
                {
                }
            }
        }
    }

    trigger OnOpenPage()
    var
        DREInteg: Codeunit "DRE Integ. DRE INF";
    begin
        DREInteg.CheckLicense();
    end;
}