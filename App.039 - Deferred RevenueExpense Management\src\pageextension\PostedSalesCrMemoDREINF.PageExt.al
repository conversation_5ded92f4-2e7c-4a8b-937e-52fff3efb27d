pageextension 71019083 "Posted Sales Cr.Memo DRE INF" extends "Posted Sales Credit Memo"
{
    layout
    {
        addlast(General)
        {
            field("Defer Rev/Exp Doc. No. DRE INF"; Rec."Defer Rev/Exp Doc. No. DRE INF")
            {
                ApplicationArea = All;
                Visible = true;
                Editable = false;

                trigger OnDrillDown()
                var
                    DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
                    DeferRevExpDREINF: Page "Defer. Rev/Exp DRE INF";
                begin
                    if Rec."Defer Rev/Exp Doc. No. DRE INF" = '' then
                        exit;

                    if DeferRevExpHeader.Get(Rec."Defer Rev/Exp Doc. No. DRE INF") then begin
                        DeferRevExpDREINF.SetRecord(DeferRevExpHeader);
                        DeferRevExpDREINF.Run();
                    end;
                end;
            }
        }
    }
}