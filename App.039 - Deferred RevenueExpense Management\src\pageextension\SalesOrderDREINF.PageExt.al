pageextension 71019084 "Sales Order DRE INF" extends "Sales Order"
{
    layout
    {
        addlast(General)
        {
            field("Defer Rev/Exp Doc. No. DRE INF"; Rec."Defer Rev/Exp Doc. No. DRE INF")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the Deferred Revenue/Expense Document Number linked to this sales document.';

                trigger OnDrillDown()
                var
                    DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
                    DeferRevExpDREINF: Page "Defer. Rev/Exp DRE INF";
                begin
                    if Rec."Defer Rev/Exp Doc. No. DRE INF" = '' then
                        exit;

                    if DeferRevExpHeader.Get(Rec."Defer Rev/Exp Doc. No. DRE INF") then begin
                        DeferRevExpDREINF.SetRecord(DeferRevExpHeader);
                        DeferRevExpDREINF.Run();
                    end;
                end;
            }
        }
    }
}