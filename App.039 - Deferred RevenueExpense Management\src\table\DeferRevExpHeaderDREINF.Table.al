table 71019082 "Defer. Rev/Exp Header DRE INF"
{
    Caption = 'Defer. Rev/Exp Header';
    DataClassification = CustomerContent;
    DrillDownPageId = "Defer. Rev/Exp List DRE INF";
    LookupPageId = "Defer. Rev/Exp List DRE INF";
    Extensible = false;

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            //NotBlank = true;
            ToolTip = 'Specifies the value of the No. field.';
        }
        field(2; "Defer. Rev/Exp Code"; Code[10])
        {
            Caption = 'Defer. Rev/Exp Code';
            TableRelation = "Defer. Rev/Exp Setup DRE INF".Code;
            ToolTip = 'Specifies the value of the Defer. Rev/Exp Code field.';
            trigger OnValidate()

            begin
                Rec.TestField("No.", '');

                if not DeferRevExpSetup.Get(Rec."Defer. Rev/Exp Code") then
                    exit;

                "No. Series" := DeferRevExpSetup."No. Series";
                if NoSeries.AreRelated(DeferRevExpSetup."No. Series", xRec."No. Series") then
                    "No. Series" := xRec."No. Series";
                "No." := NoSeries.GetNextNo("No. Series");

                //NoSeries.InitSeries(DeferRevExpSetup."No. Series", xRec."No. Series", 0D, "No.", "No. Series");

                Rec.Validate(Description, DeferRevExpSetup.Description);
                Rec.Validate(Type, DeferRevExpSetup.Type);
                Rec.Validate("Future Months Account Type", DeferRevExpSetup."Future Months Account Type");
                Rec.Validate("Rev./Exp. Acc. - Future Months", DeferRevExpSetup."Rev./Exp. Acc. - Future Months");
                Rec.Validate("Future Years Account Type", DeferRevExpSetup."Future Years Account Type");
                Rec.Validate("Rev./Exp. Acc. - Future Years", DeferRevExpSetup."Rev./Exp. Acc. - Future Years");
                Rec.Validate("Reflection Account Type", DeferRevExpSetup."Reflection Account Type");
                Rec.Validate("Rev./Exp. Reflection Account", DeferRevExpSetup."Rev./Exp. Reflection Account");
                Rec.Validate("Calculation Method", DeferRevExpSetup."Calculation Method");
                Rec.Validate("No. of Periods", DeferRevExpSetup."Number of Periods");
                Rec.Validate("GIB Document Type", DeferRevExpSetup."GIB Document Type");
                Rec.Validate("GIB Document Description", DeferRevExpSetup."GIB Document Description");
            end;
        }
        field(11; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(3; Type; Enum "Rev./Exp. Type DRE INF")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(14; "Future Months Account Type"; Enum "Purchase Line Type")
        {
            Caption = 'Future Months Account Type';
            ValuesAllowed = 1, 10;
            ToolTip = 'Specifies the value of the Future Months Account Type field.';
        }
        field(4; "Rev./Exp. Acc. - Future Months"; Code[20])
        {
            Caption = 'Rev./Exp. Account for Future Months';
            TableRelation = if ("Future Months Account Type" = const("G/L Account")) "G/L Account"
            else
            if ("Future Months Account Type" = const("Allocation Account")) "Allocation Account";
            ToolTip = 'Specifies the value of the Rev./Exp. Account for Future Months field.';
        }
        field(19; "Future Years Account Type"; Enum "Purchase Line Type")
        {
            Caption = 'Future Years Account Type';
            ValuesAllowed = 1, 10;
            ToolTip = 'Specifies the value of the Future Years Account Type field.';
        }
        field(5; "Rev./Exp. Acc. - Future Years"; Code[20])
        {
            Caption = 'Rev./Exp. Account for Future Years';
            TableRelation = if ("Future Years Account Type" = const("G/L Account")) "G/L Account"
            else
            if ("Future Years Account Type" = const("Allocation Account")) "Allocation Account";
            ToolTip = 'Specifies the value of the Rev./Exp. Account for Future Years field.';
        }
        field(24; "Reflection Account Type"; Enum "Purchase Line Type")
        {
            Caption = 'Reflection Account Type';
            ValuesAllowed = 1, 10;
            ToolTip = 'Specifies the value of the Reflection Account Type field.';
        }
        field(6; "Rev./Exp. Reflection Account"; Code[20])
        {
            Caption = 'Rev./Exp. Reflection Account';
            TableRelation = if ("Reflection Account Type" = const("G/L Account")) "G/L Account"
            else
            if ("Reflection Account Type" = const("Allocation Account")) "Allocation Account";
            ToolTip = 'Specifies the value of the Rev./Exp. Reflection Account field.';
        }
        field(7; "Calculation Method"; Enum "Deferral Calculation Method")
        {
            Caption = 'Calculation Method';
            ValuesAllowed = 1, 2, 3, ********;
            ToolTip = 'Specifies the value of the Calculation Method field.';
            trigger OnValidate()
            begin
                case Rec."Calculation Method" of
                    "Deferral Calculation Method"::"Days as Period DRE INF":
                        "No. of Periods" := DeferRevExpMngmt.CalculateDaysBetweenDates("Starting Date", "Ending Date");
                    else
                        "No. of Periods" := DeferRevExpMngmt.CalculateMonthQuantityBetweenTwoDates("Starting Date", "Ending Date");
                end;
            end;
        }
        field(8; "No. of Periods"; Integer)
        {
            Caption = 'Number of Periods';
            ToolTip = 'Specifies the value of the Number of Periods field.';
        }
        field(9; "GIB Document Type"; Enum "GIB Document Type INF")
        {
            Caption = 'GIB Document Type';
            ToolTip = 'Specifies the value of the GIB Document Type field.';
        }
        field(10; "GIB Document Description"; Text[100])
        {
            Caption = 'GIB Document Description';
            ToolTip = 'Specifies the value of the GIB Document Description field.';
        }
        field(12; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
            trigger OnValidate()
            begin
                if "Source Document No." = '' then
                    "Currency Factor" := CurrExchRate.ExchangeRate("Posting Date", "Currency Code");

                if "Closing Document" then
                    Rec.Validate("Starting Date", "Posting Date");
            end;
        }
        field(13; "External Document No."; Code[20])
        {
            Caption = 'External Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(15; "Source No."; Code[20])
        {
            Caption = 'Source No.';
            TableRelation = if (Type = const(Revenue)) Customer."No." else
            if (Type = const(Expense)) Vendor."No.";
            ToolTip = 'Specifies the value of the Source No. field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
                Vendor:
                        Record Vendor;
            begin
                if Type = Type::Revenue then begin
                    Customer.Get(Rec."Source No.");
                    Rec.Validate("Source Name", Customer.Name);
                    Rec.Validate("Currency Code", Customer."Currency Code");
                end
                else
                    if Type = Type::Expense then begin
                        Vendor.Get(Rec."Source No.");
                        Rec.Validate("Source Name", Vendor.Name);
                        Rec.Validate("Currency Code", Vendor."Currency Code");
                    end;
            end;
        }
        field(16; "Source Name"; Text[100])
        {
            Caption = 'Source Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Source Name field.';
        }
        field(17; "Initial Amount to Defer"; Decimal)
        {
            Caption = 'Initial Amount to Defer';
            ToolTip = 'Specifies the value of the Initial Amount to Defer field.';
            DecimalPlaces = 2 : 2;
        }
        field(18; "Deferred Amount"; Decimal)
        {
            Caption = 'Deferred Amount';
            DecimalPlaces = 2 : 2;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Defer. Rev/Exp Line DRE INF".Amount where("Document No." = field("No."), Posted = const(true)));
            ToolTip = 'Specifies the value of the Deferred Amount field.';
        }
        field(20; "Posting Description"; Text[100])
        {
            Caption = 'Posting Description';
            ToolTip = 'Specifies the value of the Posting Description field.';
        }
        field(21; Status; Enum "Sales Document Status")
        {
            Caption = 'Status';
            ValuesAllowed = 0, 1;
            Editable = false;
            ToolTip = 'Specifies the value of the Status field.';
            trigger OnValidate()
            begin
                case Rec.Status of
                    Rec.Status::Released:
                        begin
                            Rec.CalcFields("Total Line Amount");
                            Rec.TestField("Initial Amount to Defer", Rec."Total Line Amount");
                            Rec.TestField("Source No.");
                        end;
                    Rec.Status::Open:
                        begin
                            Rec.CalcFields("Deferred Amount");
                            Rec.TestField("Deferred Amount", 0);
                        end;
                end;
            end;
        }
        field(22; Closed; Boolean)
        {
            Caption = 'Closed';
            ToolTip = 'Specifies the value of the Closed field.';
            trigger OnValidate()
            begin
                // Rec.CalcFields("Deferred Amount");
                // Rec.TestField("Initial Amount to Defer", Rec."Deferred Amount");
            end;
        }
        field(23; "Total Line Amount"; Decimal)
        {
            Caption = 'Total Line Amount';
            DecimalPlaces = 2 : 2;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Defer. Rev/Exp Line DRE INF".Amount where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Line Amount field.';
        }
        field(25; "Starting Date"; Date)
        {
            Caption = 'Starting Date';
            ToolTip = 'Specifies the value of the Starting Date field.';
            trigger OnValidate()
            begin
                case Rec."Calculation Method" of
                    "Deferral Calculation Method"::"Days as Period DRE INF":
                        "No. of Periods" := DeferRevExpMngmt.CalculateDaysBetweenDates("Starting Date", "Ending Date");
                    else
                        "No. of Periods" := DeferRevExpMngmt.CalculateMonthQuantityBetweenTwoDates("Starting Date", "Ending Date");
                end;
            end;
        }
        field(26; "Ending Date"; Date)
        {
            Caption = 'Ending Date';
            ToolTip = 'Specifies the value of the Ending Date field.';
            trigger OnValidate()
            begin
                Rec.TestField("Source Document No.", '');

                case Rec."Calculation Method" of
                    "Deferral Calculation Method"::"Days as Period DRE INF":
                        "No. of Periods" := DeferRevExpMngmt.CalculateDaysBetweenDates("Starting Date", "Ending Date");
                    else
                        "No. of Periods" := DeferRevExpMngmt.CalculateMonthQuantityBetweenTwoDates("Starting Date", "Ending Date");
                end;
            end;
        }
        field(27; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            TableRelation = Currency.Code;
            ToolTip = 'Specifies the value of the Currency Code field.';
            trigger OnValidate()
            begin
                Rec.TestField("Source Document No.", '');

                "Currency Factor" := CurrExchRate.ExchangeRate("Posting Date", "Currency Code");
            end;
        }
        field(28; "Currency Factor"; Decimal)
        {
            Caption = 'Currency Factor';
            DecimalPlaces = 0 : 15;
            Editable = false;
            MinValue = 0;
            AllowInCustomizations = Always;
        }
        field(29; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Source Document No. field.';
        }

        field(32; "Shortcut Dimension 1 Code"; Code[20])
        {
            CaptionClass = '1,2,1';
            Caption = 'Shortcut Dimension 1 Code';
            TableRelation = "Dimension Value".Code where("Global Dimension No." = const(1),
                                                          Blocked = const(false));
            ToolTip = 'Specifies the value of the Shortcut Dimension 1 Code field.';
            trigger OnValidate()
            begin
                Rec.ValidateShortcutDimCode(1, "Shortcut Dimension 1 Code");
            end;
        }
        field(33; "Shortcut Dimension 2 Code"; Code[20])
        {
            CaptionClass = '1,2,2';
            Caption = 'Shortcut Dimension 2 Code';
            TableRelation = "Dimension Value".Code where("Global Dimension No." = const(2),
                                                          Blocked = const(false));
            ToolTip = 'Specifies the value of the Shortcut Dimension 2 Code field.';
            trigger OnValidate()
            begin
                Rec.ValidateShortcutDimCode(2, "Shortcut Dimension 2 Code");
            end;
        }
        field(31; "Create Invoice"; Boolean)
        {
            Caption = 'Create Invoice Instead of Journal Voucher';
            ToolTip = 'Specifies the value of the Create Invoice field.';
            ObsoleteState = Pending;
            ObsoleteReason = 'Replaced by Document Type field';
            ObsoleteTag = '24.0';
        }

        field(35; "Document Type"; Enum "Defer Document Type DRE INF")
        {
            Caption = 'Document Type';
            ToolTip = 'Specifies which type of document to create (Journal, Order, or Invoice).';

            // trigger OnValidate()
            // begin
            //     // For backward compatibility
            //     if "Document Type" = "Document Type"::Invoice then
            //         "Create Invoice" := true
            //     else
            //         "Create Invoice" := false;
            // end;
        }
        field(30; "Posted Document No."; Code[20])
        {
            Caption = 'Posted Document No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Posted Document No. field.';
            trigger OnValidate()
            begin
                DeferRevExpMngmt.ProcessClosingDocumentOnPostedDocNoValidation(Rec);
            end;
        }
        field(34; "Use Last Day of the Month"; Boolean)
        {
            Caption = 'Use Last Day of the Month';
            ToolTip = 'Specifies the value of the Use Last Day of the Month field.';
        }
        field(36; "Created Document No."; Code[20])
        {
            Caption = 'Created Document No.';
            Editable = false;
            ToolTip = 'Specifies the document number of the sales or purchase document created from this deferred revenue/expense.';
        }
        field(37; "Closing Document"; Boolean)
        {
            Caption = 'Closing Document';
        }


        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }

        field(480; "Dimension Set ID"; Integer)
        {
            Caption = 'Dimension Set ID';
            Editable = false;
            TableRelation = "Dimension Set Entry";
            AllowInCustomizations = Never;

            trigger OnLookup()
            begin
                Rec.ShowDimensions();
            end;

            trigger OnValidate()
            begin
                DimMgt.UpdateGlobalDimFromDimSetID("Dimension Set ID", "Shortcut Dimension 1 Code", "Shortcut Dimension 2 Code");
            end;
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    begin
        if not DeferRevExpSetup.Get(Rec."Defer. Rev/Exp Code") then
            exit;

        DeferRevExpSetup.TestField("No. Series");

        if "No." = '' then begin
            "No. Series" := DeferRevExpSetup."No. Series";
            if NoSeries.AreRelated(DeferRevExpSetup."No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;

        //NoSeries.InitSeries(DeferRevExpSetup."No. Series", xRec."No. Series", 0D, "No.", "No. Series");
    end;

    trigger OnDelete()
    var
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
    begin
        Rec.CalcFields("Deferred Amount");
        Rec.TestField("Deferred Amount", 0);

        Rec.TestField("Posted Document No.", '');

        DeferRevExpLine.SetRange("Document No.", Rec."No.");
        DeferRevExpLine.DeleteAll(true);
    end;

    internal procedure ShowDimensions()
    var
        SinglePlaceHolderLbl: Label '%1', Comment = '%1 is placeholder';
    begin

        "Dimension Set ID" :=
          DimMgt.EditDimensionSet(
            Rec, "Dimension Set ID", StrSubstNo(SinglePlaceHolderLbl, "No."),
            "Shortcut Dimension 1 Code", "Shortcut Dimension 2 Code");
    end;

    internal procedure ValidateShortcutDimCode(FieldNumber: Integer; var ShortcutDimCode: Code[20])

    var
        DeferRevExpLine: Record "Defer. Rev/Exp Line DRE INF";
        Text064Qst: Label 'You may have changed a dimension.\\Do you want to update the lines?';
    begin
        DimMgt.ValidateShortcutDimValues(FieldNumber, ShortcutDimCode, "Dimension Set ID");

        DeferRevExpLine.Reset();
        DeferRevExpLine.SetRange("Document No.", Rec."No.");
        if DeferRevExpLine.FindSet() then
            if ConfirmManagement.GetResponseOrDefault(Text064Qst, true) then
                repeat
                    DeferRevExpLine.Validate("Dimension Set ID", Rec."Dimension Set ID");
                    DeferRevExpLine.Modify(true);
                until DeferRevExpLine.Next() = 0;

    end;

    var
        CurrExchRate: Record "Currency Exchange Rate";
        DeferRevExpSetup: Record "Defer. Rev/Exp Setup DRE INF";
        //NoSeries: Codeunit NoSeriesManagement;
        NoSeries: Codeunit "No. Series";
        DeferRevExpMngmt: Codeunit "Defer. Rev/Exp. Mngmt. DRE INF";
        DimMgt: Codeunit DimensionManagement;
        ConfirmManagement: Codeunit "Confirm Management";

}