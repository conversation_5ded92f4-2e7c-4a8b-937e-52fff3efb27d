table 71019083 "Defer. Rev/Exp Line DRE INF"
{
    Caption = 'Defer. Rev/Exp Line';
    DataClassification = CustomerContent;
    Extensible = false;
    DrillDownPageId = "Defer. Rev/Exp Lines DRE INF";
    LookupPageId = "Defer. Rev/Exp Lines DRE INF";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(4; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(5; Amount; Decimal)
        {
            Caption = 'Amount';
            ToolTip = 'Specifies the value of the Amount field.';
            DecimalPlaces = 2 : 2;
        }
        field(6; Posted; Boolean)
        {
            Caption = 'Posted';
            ToolTip = 'Specifies the value of the Posted field.';
            trigger OnValidate()
            var
                DeferRevExpHeader: Record "Defer. Rev/Exp Header DRE INF";
            begin
                DeferRevExpHeader.Get(Rec."Document No.");
                DeferRevExpHeader.TestField(Status, DeferRevExpHeader.Status::Released);
            end;
        }
        field(7; Status; Enum "Sales Document Status")
        {
            Caption = 'Status';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Defer. Rev/Exp Header DRE INF".Status where("No." = field("Document No.")));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Status field.';
        }
        field(8; "Shortcut Dimension 1 Code"; Code[20])
        {
            CaptionClass = '1,2,1';
            Caption = 'Shortcut Dimension 1 Code';
            TableRelation = "Dimension Value".Code where("Global Dimension No." = const(1),
                                                          Blocked = const(false));
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Shortcut Dimension 1 Code field.';
            trigger OnValidate()
            begin
                ValidateShortcutDimCode(1, "Shortcut Dimension 1 Code");
            end;
        }
        field(9; "Shortcut Dimension 2 Code"; Code[20])
        {
            CaptionClass = '1,2,2';
            Caption = 'Shortcut Dimension 2 Code';
            TableRelation = "Dimension Value".Code where("Global Dimension No." = const(2),
                                                          Blocked = const(false));
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Shortcut Dimension 2 Code field.';
            trigger OnValidate()
            begin
                ValidateShortcutDimCode(2, "Shortcut Dimension 2 Code");
            end;
        }
        field(10; "Currency Factor"; Decimal)
        {
            Caption = 'Currency Factor';
            ToolTip = 'Specifies the value of the Currency Factor field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Defer. Rev/Exp Header DRE INF"."Currency Factor" where("No." = field("Document No.")));
        }
        field(11; "Currency Code"; Code[10])
        {
            Caption = 'Currency Code';
            ToolTip = 'Specifies the value of the Currency Code field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Defer. Rev/Exp Header DRE INF"."Currency Code" where("No." = field("Document No.")));
        }
        field(12; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            ToolTip = 'Specifies the source document number that this deferred revenue/expense line is related to.';
            AllowInCustomizations = Always;
        }
        field(13; "Source Document Line No."; Integer)
        {
            Caption = 'Source Document Line No.';
            ToolTip = 'Specifies the source document line number that this deferred revenue/expense line is related to.';
            AllowInCustomizations = Always;
        }

        field(14; Closed; Boolean)
        {
            Caption = 'Closed';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Defer. Rev/Exp Header DRE INF".Closed where("No." = field("Document No.")));
            ToolTip = 'Specifies whether the related header is closed.';
        }

        field(480; "Dimension Set ID"; Integer)
        {
            Caption = 'Dimension Set ID';
            Editable = false;
            TableRelation = "Dimension Set Entry";
            AllowInCustomizations = Never;
            ToolTip = 'Specifies the value of the Dimension Set ID field.';
            trigger OnLookup()
            begin
                ShowDimensions();
            end;

            trigger OnValidate()
            begin
                DimMgt.UpdateGlobalDimFromDimSetID("Dimension Set ID", "Shortcut Dimension 1 Code", "Shortcut Dimension 2 Code");
            end;
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
            // SumIndexFields = Amount;
        }
        key(Key2; "Document No.", Posted)
        {
            SumIndexFields = Amount;
        }
        key(Key3; "Posting Date")
        {
            // Adding key for Posting Date to optimize filtering operations
        }
        // key(Key3; Amount)
        // {
        //     SumIndexFields = Amount;
        // }
    }
    trigger OnInsert()
    var
        DeferRevExpLineDREINF: Record "Defer. Rev/Exp Line DRE INF";
    begin
        DeferRevExpLineDREINF.SetRange("Document No.", Rec."Document No.");
        if DeferRevExpLineDREINF.FindLast() then
            Rec."Line No." := DeferRevExpLineDREINF."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnDelete()
    begin
        Rec.TestField(Posted, false);
    end;

    internal procedure ValidateShortcutDimCode(FieldNumber: Integer; var ShortcutDimCode: Code[20])

    begin
        DimMgt.ValidateShortcutDimValues(FieldNumber, ShortcutDimCode, "Dimension Set ID");
    end;

    internal procedure ShowDimensions()
    var
        SinglePlaceHolderTok: Label '%1', Comment = '%1 is placeholder', Locked = true;
    begin

        "Dimension Set ID" :=
          DimMgt.EditDimensionSet(
            Rec, "Dimension Set ID", StrSubstNo(SinglePlaceHolderTok, "Document No."),
            "Shortcut Dimension 1 Code", "Shortcut Dimension 2 Code");
    end;

    var
        DimMgt: Codeunit DimensionManagement;
}