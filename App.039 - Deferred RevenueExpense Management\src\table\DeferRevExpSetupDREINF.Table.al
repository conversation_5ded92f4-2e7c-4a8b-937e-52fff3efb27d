table 71019081 "Defer. Rev/Exp Setup DRE INF"
{
    Caption = 'Deferred Revenue/Expense Setup';
    DataClassification = CustomerContent;
    LookupPageId = "Defer. Rev/Exp Setup DRE INF";
    DrillDownPageId = "Defer. Rev/Exp Setup DRE INF";
    Extensible = false;

    fields
    {
        field(1; "Code"; Code[10])
        {
            Caption = 'Code';
            //NotBlank = true;
            ToolTip = 'Specifies the value of the Code field.';
        }
        field(2; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(3; Type; Enum "Rev./Exp. Type DRE INF")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(14; "Future Months Account Type"; Enum "Purchase Line Type")
        {
            Caption = 'Future Months Account Type';
            ValuesAllowed = 1, 10;
            ToolTip = 'Specifies the value of the Future Months Account Type field.';
        }

        field(4; "Rev./Exp. Acc. - Future Months"; Code[20])
        {
            Caption = 'Rev./Exp. Account for Future Months';
            TableRelation = if ("Future Months Account Type" = const("G/L Account")) "G/L Account"
            else
            if ("Future Months Account Type" = const("Allocation Account")) "Allocation Account";
            ToolTip = 'Specifies the value of the Rev./Exp. Account for Future Months field.';
        }
        field(15; "Future Years Account Type"; Enum "Purchase Line Type")
        {
            Caption = 'Future Years Account Type';
            ValuesAllowed = 1, 10;
            ToolTip = 'Specifies the value of the Future Years Account Type field.';
        }
        field(5; "Rev./Exp. Acc. - Future Years"; Code[20])
        {
            Caption = 'Rev./Exp. Account for Future Years';
            TableRelation = if ("Future Years Account Type" = const("G/L Account")) "G/L Account"
            else
            if ("Future Years Account Type" = const("Allocation Account")) "Allocation Account";
            ToolTip = 'Specifies the value of the Rev./Exp. Account for Future Years field.';
        }
        field(16; "Reflection Account Type"; Enum "Purchase Line Type")
        {
            Caption = 'Reflection Account Type';
            ValuesAllowed = 1, 10;
            ToolTip = 'Specifies the value of the Reflection Account Type field.';
        }
        field(6; "Rev./Exp. Reflection Account"; Code[20])
        {
            Caption = 'Rev./Exp. Reflection Account';
            TableRelation = if ("Reflection Account Type" = const("G/L Account")) "G/L Account"
            else
            if ("Reflection Account Type" = const("Allocation Account")) "Allocation Account";
            ToolTip = 'Specifies the value of the Rev./Exp. Reflection Account field.';
        }
        field(7; "Calculation Method"; Enum "Deferral Calculation Method")
        {
            Caption = 'Calculation Method';
            ValuesAllowed = 1, 2, 3, ********;
            ToolTip = 'Specifies the value of the Calculation Method field.';
        }
        field(8; "Number of Periods"; Integer)
        {
            Caption = 'Number of Periods';
            ToolTip = 'Specifies the value of the Number of Periods field.';
        }
        field(9; "GIB Document Type"; Enum "GIB Document Type INF")
        {
            Caption = 'GIB Document Type';
            ToolTip = 'Specifies the value of the GIB Document Type field.';
        }
        field(10; "GIB Document Description"; Text[100])
        {
            Caption = 'GIB Document Description';
            TableRelation = "GIB Document Desciption INF".Description where("Document Type" = field("GIB Document Type"));
            ToolTip = 'Specifies the value of the GIB Document Description field.';
        }
        field(11; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the value of the No. Series field.';
        }
        field(12; "Journal Template Name"; Code[10])
        {
            Caption = 'Journal Template Name';
            TableRelation = "Gen. Journal Template";
            ToolTip = 'Specifies the value of the Journal Template Name field.';
        }
        field(13; "Journal Batch Name"; Code[10])
        {
            Caption = 'Journal Batch Name';
            TableRelation = "Gen. Journal Batch".Name where("Journal Template Name" = field("Journal Template Name"));
            ToolTip = 'Specifies the value of the Journal Batch Name field.';
        }
        // field(14; "Deferral Code"; Code[10])
        // {
        //     Caption = 'Deferral Code';
        //     TableRelation = "Deferral Template";
        // }
    }
    keys
    {
        key(PK; Code)
        {
            Clustered = true;
        }
    }
}