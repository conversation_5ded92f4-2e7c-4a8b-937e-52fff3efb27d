tableextension 71019083 "G/L Entry DRE INF" extends "G/L Entry"
{
    fields
    {
        field(71019080; "Defer. RevExp Doc. No. DRE INF"; Code[20])
        {
            Caption = 'Defer. Rev./Exp. Doc. No.';
            DataClassification = CustomerContent;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Defer. Rev./Exp. Doc. No. field.';
        }
        field(71019081; "DeferRevExpDocLine No. DRE INF"; Integer)
        {
            Caption = 'Defer. Rev./Exp. Doc. Line No.';
            DataClassification = CustomerContent;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Defer. Rev./Exp. Doc. Line No. field.';
        }
    }
}